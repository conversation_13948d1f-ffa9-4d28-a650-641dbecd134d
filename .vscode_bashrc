# VSCode-specific bashrc for easywin_ai_rapid project

# Source the system bashrc first (but skip pyenv parts)
if [ -f ~/.bashrc ]; then
    # Source bashrc but skip pyenv initialization
    source <(grep -v pyenv ~/.bashrc)
fi

# Remove pyenv from PATH to avoid interference, but preserve other PATH entries
if command -v pyenv >/dev/null 2>&1; then
    # Remove pyenv paths from PATH while preserving other entries
    export PATH=$(echo "$PATH" | tr ':' '\n' | grep -v pyenv | tr '\n' ':' | sed 's/:$//')
fi

# Ensure standard system paths are included
export PATH="/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:$PATH"

# Set up basic shell options
set -o emacs
shopt -s checkwinsize
shopt -s histappend

# Basic prompt if not set
if [ -z "$PS1" ]; then
    PS1='\u@\h:\w\$ '
fi

# Navigate to project directory
cd /home/<USER>/project/easywin_ai_rapid

# Activate virtual environment
if [ -f "backend/.venv/bin/activate" ]; then
    echo "Activating virtual environment..."
    source backend/.venv/bin/activate
    echo "Virtual environment activated: $(python --version)"
else
    echo "Warning: Virtual environment not found at backend/.venv"
fi

# Set PYTHONPATH for backend development
export PYTHONPATH="/home/<USER>/project/easywin_ai_rapid/backend:$PYTHONPATH"

echo "Ready for development!"
