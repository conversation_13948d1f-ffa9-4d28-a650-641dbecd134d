["backend/test_basic_api.py::TestBasicAPI::test_app_index", "backend/test_basic_api.py::TestBasicAPI::test_auth_login_username_password", "backend/test_basic_api.py::TestBasicAPI::test_auth_me", "backend/test_basic_api.py::TestBasicAPI::test_auth_me_without_token", "backend/test_basic_api.py::TestBasicAPI::test_auth_register", "backend/test_basic_api.py::TestBasicAPI::test_companies_create", "backend/test_basic_api.py::TestBasicAPI::test_companies_search", "backend/test_basic_api.py::TestBasicAPI::test_error_handling", "backend/test_basic_api.py::TestBasicAPI::test_sms_code_send", "backend/test_basic_api.py::TestBasicAPI::test_user_profile_get", "backend/test_basic_api.py::TestBasicAPI::test_user_profile_update", "backend/test_basic_models.py::TestBasicModels::test_bid_model", "backend/test_basic_models.py::TestBasicModels::test_company_model", "backend/test_basic_models.py::TestBasicModels::test_model_relationships_comprehensive", "backend/test_basic_models.py::TestBasicModels::test_user_company_relationship", "backend/test_basic_models.py::TestBasicModels::test_user_model", "backend/test_company_management.py::test_company_management", "backend/test_setup.py::test_app_creation", "backend/test_setup.py::test_database_connection", "backend/test_setup.py::test_models_import", "backend/test_setup.py::test_routes_import", "backend/test_simple.py::test_api_imports", "backend/test_simple.py::test_basic_functionality", "backend/test_simple.py::test_jwt_functionality", "backend/tests/test_auth_api.py::TestLegacyAuthApi::test_placeholder", "backend/tests/test_auth_routes.py::TestAuthLogin::test_login_missing_credentials", "backend/tests/test_auth_routes.py::TestAuthLogin::test_phone_code_login_invalid_code", "backend/tests/test_auth_routes.py::TestAuthLogin::test_phone_code_login_success_existing_user", "backend/tests/test_auth_routes.py::TestAuthLogin::test_phone_code_login_success_new_user", "backend/tests/test_auth_routes.py::TestAuthLogout::test_logout_success", "backend/tests/test_auth_routes.py::TestAuthLogout::test_logout_without_token", "backend/tests/test_auth_routes.py::TestAuthMe::test_get_current_user_invalid_token", "backend/tests/test_auth_routes.py::TestAuthMe::test_get_current_user_success", "backend/tests/test_auth_routes.py::TestAuthMe::test_get_current_user_without_token", "backend/tests/test_auth_routes.py::TestSMSCode::test_send_sms_code_invalid_phone", "backend/tests/test_auth_routes.py::TestSMSCode::test_send_sms_code_success", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_alipay_login_new_user", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_check_login_status_invalid_state", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_check_login_status_pending", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_get_alipay_qr", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_get_wechat_qr", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_login_inactive_user", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_login_invalid_credentials", "backend/tests/test_auth_routes.py::TestThirdPartyLogin::test_wechat_login_new_user", "backend/tests/test_bid_import.py::TestBidImportAPI::test_import_bid_invalid_company", "backend/tests/test_bid_import.py::TestBidImportAPI::test_import_bid_no_file", "backend/tests/test_bid_import.py::TestBidImportAPI::test_import_bid_no_title", "backend/tests/test_bid_import.py::TestBidImportAPI::test_import_bid_success", "backend/tests/test_bid_import.py::TestBidImportAPI::test_import_bid_unauthorized", "backend/tests/test_bid_import.py::TestDocumentParser::test_create_bid_from_parsed_content", "backend/tests/test_bid_import.py::TestDocumentParser::test_parse_docx_file", "backend/tests/test_bid_routes.py::TestBidCreate::test_create_bid_invalid_company", "backend/tests/test_bid_routes.py::TestBidCreate::test_create_bid_missing_title", "backend/tests/test_bid_routes.py::TestBidCreate::test_create_bid_success", "backend/tests/test_bid_routes.py::TestBidCreate::test_create_bid_with_documents", "backend/tests/test_bid_routes.py::TestBidDelete::test_delete_bid_not_found", "backend/tests/test_bid_routes.py::TestBidDelete::test_delete_bid_success", "backend/tests/test_bid_routes.py::TestBidDetail::test_get_bid_no_permission", "backend/tests/test_bid_routes.py::TestBidDetail::test_get_bid_not_found", "backend/tests/test_bid_routes.py::TestBidDetail::test_get_bid_success", "backend/tests/test_bid_routes.py::TestBidExport::test_export_bid_no_permission", "backend/tests/test_bid_routes.py::TestBidExport::test_export_bid_not_found", "backend/tests/test_bid_routes.py::TestBidExport::test_export_bid_success", "backend/tests/test_bid_routes.py::TestBidGenerate::test_generate_bid_invalid_tender_doc", "backend/tests/test_bid_routes.py::TestBidGenerate::test_generate_bid_missing_tender_docs", "backend/tests/test_bid_routes.py::TestBidGenerate::test_generate_bid_success", "backend/tests/test_bid_routes.py::TestBidList::test_get_bids_pagination", "backend/tests/test_bid_routes.py::TestBidList::test_get_bids_success", "backend/tests/test_bid_routes.py::TestBidList::test_get_bids_with_filters", "backend/tests/test_bid_routes.py::TestBidList::test_get_bids_without_auth", "backend/tests/test_bid_routes.py::TestBidUpdate::test_update_bid_invalid_deadline", "backend/tests/test_bid_routes.py::TestBidUpdate::test_update_bid_not_found", "backend/tests/test_bid_routes.py::TestBidUpdate::test_update_bid_success", "backend/tests/test_company_routes.py::TestCompanyCreate::test_create_company_duplicate_name", "backend/tests/test_company_routes.py::TestCompanyCreate::test_create_company_missing_name", "backend/tests/test_company_routes.py::TestCompanyCreate::test_create_company_success", "backend/tests/test_company_routes.py::TestCompanyCreate::test_create_company_without_auth", "backend/tests/test_company_routes.py::TestCompanyDetail::test_get_company_success", "backend/tests/test_company_routes.py::TestCompanySearch::test_search_companies_empty_query", "backend/tests/test_company_routes.py::TestCompanySearch::test_search_companies_no_results", "backend/tests/test_company_routes.py::TestCompanySearch::test_search_companies_success", "backend/tests/test_company_routes.py::TestCompanySearch::test_search_companies_without_auth", "backend/tests/test_company_routes.py::TestCompanyUpdate::test_update_company_duplicate_name", "backend/tests/test_company_routes.py::TestCompanyUpdate::test_update_company_no_permission", "backend/tests/test_company_routes.py::TestCompanyUpdate::test_update_company_success", "backend/tests/test_company_routes.py::TestCompanyUserList::test_get_user_companies_empty", "backend/tests/test_company_routes.py::TestCompanyUserList::test_get_user_companies_success", "backend/tests/test_company_routes.py::TestCompanyUserList::test_get_user_companies_without_auth", "backend/tests/test_document_routes.py::TestDocumentDetail::test_get_tender_document_not_found", "backend/tests/test_document_routes.py::TestDocumentDetail::test_get_tender_document_success", "backend/tests/test_document_routes.py::TestDocumentDownload::test_download_file_success", "backend/tests/test_document_routes.py::TestDocumentDownload::test_download_file_without_auth", "backend/tests/test_document_routes.py::TestTenderDocumentUploadPlaceholder::test_placeholder", "backend/tests/test_document_routes.py::TestTenderDocumentUploadPlaceholder::test_upload_tender_document_invalid_type", "backend/tests/test_document_routes.py::TestTenderDocumentUploadPlaceholder::test_upload_tender_document_no_file", "backend/tests/test_document_routes.py::TestTenderDocumentUploadPlaceholder::test_upload_tender_document_without_auth", "backend/tests/test_models.py::TestBidHistoryModel::test_create_history", "backend/tests/test_models.py::TestBidHistoryModel::test_get_bid_history", "backend/tests/test_models.py::TestBidIntermediateModel::test_create_intermediate", "backend/tests/test_models.py::TestBidIntermediateModel::test_intermediate_to_dict", "backend/tests/test_models.py::TestBidModel::test_bid_relationships", "backend/tests/test_models.py::TestBidModel::test_bid_to_dict", "backend/tests/test_models.py::TestBidModel::test_create_bid", "backend/tests/test_models.py::TestBidNodeModel::test_bid_node_children", "backend/tests/test_models.py::TestBidNodeModel::test_bid_node_to_dict", "backend/tests/test_models.py::TestBidNodeModel::test_create_bid_node", "backend/tests/test_models.py::TestBidNodeModel::test_update_node_content", "backend/tests/test_models.py::TestBidReferenceModel::test_create_bid_reference", "backend/tests/test_models.py::TestBidRootNode::test_create_root_node", "backend/tests/test_models.py::TestBidRootNode::test_get_root_node", "backend/tests/test_models.py::TestCompanyInformationTypeModel::test_create_company_info_type", "backend/tests/test_models.py::TestCompanyModel::test_company_to_dict", "backend/tests/test_models.py::TestCompanyModel::test_create_company", "backend/tests/test_models.py::TestFileStorageModel::test_calculate_file_hash", "backend/tests/test_models.py::TestFileStorageModel::test_create_file_storage", "backend/tests/test_models.py::TestFileStorageModel::test_file_path_property", "backend/tests/test_models.py::TestTenderDocumentModel::test_create_tender_document", "backend/tests/test_models.py::TestTenderDocumentModel::test_tender_document_to_dict", "backend/tests/test_models.py::TestTenderInformationTypeModel::test_create_tender_info_type", "backend/tests/test_models.py::TestUserModel::test_create_user", "backend/tests/test_models.py::TestUserModel::test_user_relationships", "backend/tests/test_models.py::TestUserModel::test_user_to_dict", "backend/tests/test_node_routes.py::TestNodeRoutes::test_create_node_invalid_data", "backend/tests/test_node_routes.py::TestNodeRoutes::test_create_node_with_parent", "backend/tests/test_node_routes.py::TestNodeRoutes::test_create_text_node", "backend/tests/test_node_routes.py::TestNodeRoutes::test_delete_node", "backend/tests/test_node_routes.py::TestNodeRoutes::test_get_bid_history", "backend/tests/test_node_routes.py::TestNodeRoutes::test_get_bid_nodes_empty", "backend/tests/test_node_routes.py::TestNodeRoutes::test_get_node_details", "backend/tests/test_node_routes.py::TestNodeRoutes::test_invalid_bid_access", "backend/tests/test_node_routes.py::TestNodeRoutes::test_move_node", "backend/tests/test_node_routes.py::TestNodeRoutes::test_unauthorized_access", "backend/tests/test_node_routes.py::TestNodeRoutes::test_update_node_content", "backend/tests/test_user_routes.py::TestUserProfile::test_get_profile_success", "backend/tests/test_user_routes.py::TestUserProfile::test_get_profile_without_auth", "backend/tests/test_user_routes.py::TestUserProfile::test_patch_profile_success", "backend/tests/test_user_routes.py::TestUserProfile::test_update_profile_empty_data", "backend/tests/test_user_routes.py::TestUserProfile::test_update_profile_invalid_field", "backend/tests/test_user_routes.py::TestUserProfile::test_update_profile_long_username", "backend/tests/test_user_routes.py::TestUserProfile::test_update_profile_success", "backend/tests/test_user_routes.py::TestUserProfile::test_update_profile_unicode_username", "backend/tests/test_user_routes.py::TestUserProfile::test_update_profile_without_auth"]