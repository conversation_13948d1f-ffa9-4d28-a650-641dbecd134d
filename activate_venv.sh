#!/bin/bash

# Quick script to activate the virtual environment with clean PATH
# Usage: source activate_venv.sh

echo "Activating virtual environment with clean PATH..."

# Remove pyenv from PATH to avoid interference, but preserve other PATH entries
if command -v pyenv >/dev/null 2>&1; then
    # Remove pyenv paths from PATH while preserving other entries
    export PATH=$(echo "$PATH" | tr ':' '\n' | grep -v pyenv | tr '\n' ':' | sed 's/:$//')
fi

# Ensure standard system paths are included
export PATH="/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:$PATH"

# Activate the virtual environment
source backend/.venv/bin/activate

echo "Virtual environment activated!"
echo "Python: $(which python)"
echo "Python version: $(python --version)"
echo ""
echo "You can now run Python commands with the correct environment."
echo "To deactivate, run: deactivate"
